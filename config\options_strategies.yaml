# 🎯 Options Trading Strategies Configuration
# Comprehensive strategies for backtesting and live trading for NIFTY & BANKNIFTY

strategies:
  # Volatility-based strategies
  volatility_breakout_ce:
    name: "Volatility Breakout Call"
    type: "volatility"
    market_outlook: "bullish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 60"
        - "underlying_above_ema_20"
        - "volume > avg_volume_20"
      confidence_threshold: 0.6
      max_signals_per_day: 5
    
  volatility_breakout_pe:
    name: "Volatility Breakout Put"
    type: "volatility"
    market_outlook: "bearish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 40"
        - "underlying_below_ema_20"
        - "volume > avg_volume_20"
      confidence_threshold: 0.6
      max_signals_per_day: 5

  # Directional strategies
  momentum_long_call:
    name: "Momentum Long Call"
    type: "directional"
    market_outlook: "bullish"
    timeframes: ["3min", "5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 55"
        - "underlying_above_ema_20"
      confidence_threshold: 0.7
      max_signals_per_day: 3

  momentum_long_put:
    name: "Momentum Long Put"
    type: "directional"
    market_outlook: "bearish"
    timeframes: ["3min", "5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 45"
        - "underlying_below_ema_20"
      confidence_threshold: 0.7
      max_signals_per_day: 3

  # Mean reversion strategies
  oversold_bounce:
    name: "Oversold Bounce"
    type: "mean_reversion"
    market_outlook: "bullish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 < 30"
        - "iv_rank > 50"
      confidence_threshold: 0.8
      max_signals_per_day: 2

  overbought_fade:
    name: "Overbought Fade"
    type: "mean_reversion"
    market_outlook: "bearish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "rsi_14 > 70"
        - "iv_rank > 50"
      confidence_threshold: 0.8
      max_signals_per_day: 2

  # Flow-based strategies
  unusual_volume_ce:
    name: "Unusual Volume Call"
    type: "flow"
    market_outlook: "bullish"
    timeframes: ["1min", "3min"]
    parameters:
      entry_conditions:
        - "volume > avg_volume_20"
        - "underlying_above_ema_20"
      confidence_threshold: 0.6
      max_signals_per_day: 8

  unusual_volume_pe:
    name: "Unusual Volume Put"
    type: "flow"
    market_outlook: "bearish"
    timeframes: ["1min", "3min"]
    parameters:
      entry_conditions:
        - "volume > avg_volume_20"
        - "underlying_below_ema_20"
      confidence_threshold: 0.6
      max_signals_per_day: 8

  # Simple trend following
  trend_following_ce:
    name: "Trend Following Call"
    type: "trend"
    market_outlook: "bullish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "underlying_above_ema_20"
      confidence_threshold: 0.5
      max_signals_per_day: 10

  trend_following_pe:
    name: "Trend Following Put"
    type: "trend"
    market_outlook: "bearish"
    timeframes: ["5min", "15min"]
    parameters:
      entry_conditions:
        - "underlying_below_ema_20"
      confidence_threshold: 0.5
      max_signals_per_day: 10

  # Simple always-signal strategies for testing
  simple_call_signal:
    name: "Simple Call Signal"
    type: "simple"
    market_outlook: "bullish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions: []  # No conditions - always signal
      confidence_threshold: 0.3
      max_signals_per_day: 50

  simple_put_signal:
    name: "Simple Put Signal"
    type: "simple"
    market_outlook: "bearish"
    timeframes: ["1min", "3min", "5min"]
    parameters:
      entry_conditions: []  # No conditions - always signal
      confidence_threshold: 0.3
      max_signals_per_day: 50

  # 📊 COMPREHENSIVE BACKTESTING STRATEGIES
  # These strategies include detailed legs, entry/exit conditions for backtesting

  long_call_otm:
    name: "Long Call OTM"
    type: "long_call"
    description: "Buy OTM call options on bullish momentum"
    timeframes: ["1min", "3min", "5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      moneyness_range: [1.02, 1.05]  # 2-5% OTM
      max_dte: 30  # Days to expiry
      min_dte: 1
      entry_time: "09:30-15:00"
    entry_conditions:
      rsi_14: "> 55"
      underlying_price: "> ema_20"
      volume: "> avg_volume_20"
      iv_rank: "< 70"
    exit_conditions:
      profit_target: 0.5  # 50% profit
      stop_loss: 0.3      # 30% loss
      time_exit: "15:15"  # Exit before close
    risk_management:
      max_loss_per_trade: 1000
      position_size_pct: 0.02  # 2% of capital
      max_concurrent_positions: 3

  long_put_otm:
    name: "Long Put OTM"
    type: "long_put"
    description: "Buy OTM put options on bearish momentum"
    timeframes: ["1min", "3min", "5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      moneyness_range: [0.95, 0.98]  # 2-5% OTM
      max_dte: 30
      min_dte: 1
      entry_time: "09:30-15:00"
    entry_conditions:
      rsi_14: "< 45"
      underlying_price: "< ema_20"
      volume: "> avg_volume_20"
      iv_rank: "< 70"
    exit_conditions:
      profit_target: 0.5
      stop_loss: 0.3
      time_exit: "15:15"
    risk_management:
      max_loss_per_trade: 1000
      position_size_pct: 0.02
      max_concurrent_positions: 3

  long_straddle:
    name: "Long Straddle"
    type: "long_straddle"
    description: "Buy ATM call and put for volatility expansion"
    timeframes: ["5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      moneyness_range: [0.98, 1.02]  # ATM
      max_dte: 15
      min_dte: 1
      entry_time: "10:00-14:00"
    entry_conditions:
      iv_rank: "< 30"  # Low volatility
      upcoming_event: "true"  # Before earnings/events
      volume: "> avg_volume_10"
    exit_conditions:
      profit_target: 0.3
      stop_loss: 0.5
      time_exit: "15:00"
    risk_management:
      max_loss_per_trade: 2000
      position_size_pct: 0.03
      max_concurrent_positions: 2

  long_strangle:
    name: "Long Strangle"
    type: "long_strangle"
    description: "Buy OTM call and put for volatility expansion"
    timeframes: ["5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      call_moneyness: [1.02, 1.05]  # OTM call
      put_moneyness: [0.95, 0.98]   # OTM put
      max_dte: 15
      min_dte: 1
      entry_time: "10:00-14:00"
    entry_conditions:
      iv_rank: "< 25"
      range_bound: "true"  # Sideways market
      volume: "> avg_volume_10"
    exit_conditions:
      profit_target: 0.4
      stop_loss: 0.6
      time_exit: "15:00"
    risk_management:
      max_loss_per_trade: 1500
      position_size_pct: 0.025
      max_concurrent_positions: 2

  bull_call_spread:
    name: "Bull Call Spread"
    type: "bull_call_spread"
    description: "Buy ITM call, sell OTM call for bullish outlook"
    timeframes: ["5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      long_moneyness: [0.98, 1.00]   # ITM/ATM
      short_moneyness: [1.03, 1.06]  # OTM
      max_dte: 20
      min_dte: 3
      entry_time: "09:45-14:30"
    entry_conditions:
      rsi_14: "> 50"
      underlying_price: "> ema_20"
      trend: "bullish"
    exit_conditions:
      profit_target: 0.6  # 60% of max profit
      stop_loss: 0.4      # 40% of premium paid
      time_exit: "15:00"
    risk_management:
      max_loss_per_trade: 800
      position_size_pct: 0.015
      max_concurrent_positions: 4

  bear_put_spread:
    name: "Bear Put Spread"
    type: "bear_put_spread"
    description: "Buy ITM put, sell OTM put for bearish outlook"
    timeframes: ["5min", "15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      long_moneyness: [1.00, 1.02]   # ITM
      short_moneyness: [0.94, 0.97]  # OTM
      max_dte: 20
      min_dte: 3
      entry_time: "09:45-14:30"
    entry_conditions:
      rsi_14: "< 50"
      underlying_price: "< ema_20"
      trend: "bearish"
    exit_conditions:
      profit_target: 0.6
      stop_loss: 0.4
      time_exit: "15:00"
    risk_management:
      max_loss_per_trade: 800
      position_size_pct: 0.015
      max_concurrent_positions: 4

  iron_condor:
    name: "Iron Condor"
    type: "iron_condor"
    description: "Sell call spread and put spread for range-bound market"
    timeframes: ["15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      call_short_moneyness: [1.02, 1.04]
      call_long_moneyness: [1.05, 1.08]
      put_short_moneyness: [0.96, 0.98]
      put_long_moneyness: [0.92, 0.95]
      max_dte: 10
      min_dte: 2
      entry_time: "10:30-13:30"
    entry_conditions:
      iv_rank: "> 50"  # High volatility
      range_bound: "true"
      low_momentum: "true"
    exit_conditions:
      profit_target: 0.5  # 50% of credit received
      stop_loss: 2.0      # 200% of credit received
      time_exit: "14:30"
    risk_management:
      max_loss_per_trade: 2000
      position_size_pct: 0.02
      max_concurrent_positions: 2

  short_straddle:
    name: "Short Straddle"
    type: "short_straddle"
    description: "Sell ATM call and put for high volatility"
    timeframes: ["15min"]
    underlyings: ["NIFTY", "BANKNIFTY"]
    parameters:
      moneyness_range: [0.99, 1.01]  # ATM
      max_dte: 7
      min_dte: 1
      entry_time: "11:00-13:00"
    entry_conditions:
      iv_rank: "> 70"  # Very high volatility
      low_momentum: "true"
      range_bound: "true"
    exit_conditions:
      profit_target: 0.4
      stop_loss: 2.5
      time_exit: "14:45"
    risk_management:
      max_loss_per_trade: 3000
      position_size_pct: 0.025
      max_concurrent_positions: 1

# Strategy selection settings
strategy_selection:
  max_active_strategies: 8
  rotation_enabled: true
  rotation_interval_minutes: 30
  
  # Strategy weights for selection
  strategy_weights:
    volatility: 0.3
    directional: 0.3
    mean_reversion: 0.2
    flow: 0.15
    trend: 0.05

# Risk management per strategy
risk_management:
  default_lot_size: 1
  max_lot_size: 3
  max_daily_loss_per_strategy: 5000
  max_concurrent_signals: 5
  
# Performance tracking
performance:
  track_strategy_performance: true
  min_trades_for_evaluation: 5
  performance_window_days: 7
